[project]
name = "mcp-github-trending"
version = "0.1.0"
description = "A Model Context Protocol server providing GitHub trending repositories information for LLMs"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    { name = "<PERSON> <PERSON>", email = "<EMAIL>" },
]
keywords = ["github", "trending", "mcp", "llm"]
license = { text = "MIT" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "gtrending>=0.5.1",
    "mcp>=1.0.0",
    "pydantic>=2.0.0",
]

[project.scripts]
mcp-github-trending = "mcp_server_github_trending:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=8.3.3",
    "ruff>=0.8.1",
]

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_server_github_trending"]