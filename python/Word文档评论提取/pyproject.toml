[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-document-comment-extractor-mcp"
version = "1.0.0"
description = "Word文档评论提取MCP服务 - 提供文档评论提取相关功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
]

[project.scripts]
word-document-comment-extractor = "word_document_comment_extractor.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_document_comment_extractor*"]
