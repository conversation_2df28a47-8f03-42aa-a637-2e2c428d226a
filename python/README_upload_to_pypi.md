# PyPI 上传脚本

一个简洁高效的 Shell 脚本，用于将 Python 项目上传到 PyPI 测试仓库或正式仓库。

## 功能特性

- 🚀 **一键上传**: 支持测试仓库和正式仓库
- 🎨 **彩色输出**: 清晰的状态提示和错误信息
- 🔧 **自动依赖**: 自动检查并安装必要依赖
- 🧹 **智能清理**: 自动清理旧的构建文件
- 🔒 **安全处理**: 临时文件处理 token，用后即删
- ⚡ **错误处理**: 遇到错误立即退出
- 🛡️ **默认测试**: 默认上传到测试仓库，避免误操作

## 使用方法

### 基本语法
```bash
./upload_to_pypi.sh <项目目录名> [test|prod]
```

### 参数说明
- `项目目录名`: 要上传的 Python 项目目录
- `test|prod`: 目标仓库类型（可选，默认为 test）

### 使用示例

```bash
# 上传到测试仓库（推荐先测试）
./upload_to_pypi.sh my_package test

# 上传到正式仓库
./upload_to_pypi.sh my_package prod

# 默认上传到测试仓库
./upload_to_pypi.sh my_package
```

## 前置条件

### 项目结构要求
项目目录必须包含以下文件之一：
- `pyproject.toml` （推荐）
- `setup.py`

### 系统要求
- Python 3.6+
- pip
- 网络连接

## 安装与配置

### 1. 下载脚本
```bash
# 下载并添加执行权限
chmod +x upload_to_pypi.sh
```

### 2. 配置 Token
脚本内置了 PyPI token，如需更新，请修改脚本中的 `TEST_TOKEN` 和 `PROD_TOKEN` 变量。

### 3. 验证安装
```bash
./upload_to_pypi.sh --help
```

## 工作流程

脚本执行以下步骤：

1. **参数验证** - 检查输入参数的有效性
2. **项目验证** - 确认项目目录和配置文件存在
3. **依赖检查** - 自动安装 `twine` 和 `build` 工具
4. **环境清理** - 删除旧的 `dist/`、`build/` 目录
5. **包构建** - 使用 `python -m build` 构建包
6. **包上传** - 使用 `twine` 上传到指定仓库
7. **安装提示** - 显示包的安装命令

## 仓库配置

### 测试仓库 (test)
- URL: `https://test.pypi.org/legacy/`
- 用途: 测试包的上传和安装
- 安装命令: 
  ```bash
  pip install --index-url https://test.pypi.org/simple/ --extra-index-url https://pypi.org/simple/ <包名>
  ```

### 正式仓库 (prod)
- URL: `https://upload.pypi.org/legacy/`
- 用途: 正式发布包
- 安装命令:
  ```bash
  pip install <包名>
  ```

## 输出示例

```bash
$ ./upload_to_pypi.sh my_package test

ℹ️  开始上传项目: my_package
ℹ️  目标仓库: 测试仓库
==================================================
🔄 检查依赖...
✅ 依赖检查完成
🔄 清理旧的构建文件...
✅ 清理完成
🔄 构建包...
✅ 构建完成
ℹ️  生成的文件:
-rw-r--r-- 1 <USER> <GROUP> 1234 Jan 1 12:00 my_package-1.0.0-py3-none-any.whl
-rw-r--r-- 1 <USER> <GROUP> 5678 Jan 1 12:00 my_package-1.0.0.tar.gz
🔄 上传包到测试仓库...
✅ 上传成功到测试仓库!

==================================================
ℹ️  测试安装命令:
pip install --index-url https://test.pypi.org/simple/ --extra-index-url https://pypi.org/simple/ my_package
==================================================
✅ 脚本执行完成!
```

## 错误处理

### 常见错误及解决方案

1. **项目目录不存在**
   ```bash
   ❌ 项目目录不存在: my_package
   ```
   - 检查目录名称是否正确
   - 确保在正确的工作目录下运行脚本

2. **缺少配置文件**
   ```bash
   ❌ 项目缺少配置文件: pyproject.toml 或 setup.py
   ```
   - 添加 `pyproject.toml` 或 `setup.py` 文件

3. **构建失败**
   ```bash
   ❌ 构建失败，没有生成dist文件
   ```
   - 检查项目配置文件语法
   - 确保项目结构正确

4. **上传失败**
   ```bash
   ❌ 上传失败!
   ```
   - 检查网络连接
   - 确认 token 是否有效
   - 检查包名是否已存在（正式仓库）

## 最佳实践

### 1. 测试优先
```bash
# 1. 先上传到测试仓库
./upload_to_pypi.sh my_package test

# 2. 测试安装
pip install --index-url https://test.pypi.org/simple/ --extra-index-url https://pypi.org/simple/ my_package

# 3. 确认无误后上传到正式仓库
./upload_to_pypi.sh my_package prod
```

### 2. 版本管理
- 每次上传前更新版本号
- 遵循语义化版本控制 (SemVer)

### 3. 包配置检查
```bash
# 检查包信息
python -m build
twine check dist/*
```

## 安全注意事项

1. **Token 保护**: 脚本中的 token 应妥善保管，避免泄露
2. **临时文件**: 脚本使用临时文件处理认证信息，用后自动删除
3. **权限控制**: 确保脚本文件权限设置合理

## 故障排除

### 检查依赖
```bash
python --version
pip --version
twine --version
python -m build --version
```

### 手动构建测试
```bash
cd your_project
python -m build
twine check dist/*
```

### 查看详细日志
修改脚本，在 twine upload 命令中添加 `--verbose` 参数：
```bash
twine upload --verbose --config-file "$TEMP_PYPIRC" --repository upload dist/*
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！