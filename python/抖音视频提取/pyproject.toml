[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "douyin-mcp-server"
version = "1.2.0"
description = "MCP server for downloading Douyin videos and extracting text"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "yzfly", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.10"
dependencies = [
    "mcp>=1.0.0",
    "requests",
    "ffmpeg-python",
    "tqdm",
    "dashscope",
]

[project.urls]
Homepage = "https://github.com/yzfly/douyin-mcp-server"
Repository = "https://github.com/yzfly/douyin-mcp-server"
Issues = "https://github.com/yzfly/douyin-mcp-server/issues"

[project.scripts]
douyin-mcp-server = "douyin_mcp_server.server:main"