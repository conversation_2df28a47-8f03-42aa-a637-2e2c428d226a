[project]
name = "excel-mcp-server"
version = "0.1.7"
description = "Excel MCP Server for manipulating Excel files"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp[cli]>=1.10.1",
    "fastmcp>=2.0.0,<3.0.0",
    "openpyxl>=3.1.5",
    "typer>=0.16.0"
]
[[project.authors]]
name = "haris"
email = "<EMAIL>"

[project.scripts]
excel-mcp-server = "excel_mcp.__main__:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/excel_mcp"]

[tool.hatch.build]
packages = ["src/excel_mcp"]
