[project]
name = "video-format-converter-mcp"
version = "0.1.0"
description = "MCP server for video format conversion and property adjustment using FFmpeg"
readme = "README.md"
requires-python = ">=3.12"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["mcp", "video", "format", "conversion", "ffmpeg", "codec", "container"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Video",
    "Topic :: Multimedia :: Video :: Conversion",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "ffmpeg-python>=0.2.0",
    "mcp[cli]>=1.9.0",
]

[project.scripts]
video-format-converter-mcp = "video_format_converter_mcp.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/video_format_converter_mcp"]

[dependency-groups]
dev = [
    "build>=1.3.0",
    "twine>=6.1.0",
]
