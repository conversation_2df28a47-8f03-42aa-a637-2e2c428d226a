[project]
name = "mcp-server-everything-search"
version = "0.2.1"
description = "A Model Context Protocol server providing fast file searching using Everything SDK"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "aid<PERSON><PERSON><PERSON>@gmail.com" },
]
keywords = ["everything", "search", "mcp", "llm"]
license = { file = "LICENSE" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
]
dependencies = [
    "mcp>=1.0.0",
    "pydantic>=2.0.0",
]

[project.scripts]
mcp-server-everything-search = "mcp_server_everything_search:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pyright>=1.1.389",
    "pytest>=8.3.3",
    "ruff>=0.8.1",
]
