[project]
name = "video-content-extractor-mcp"
version = "0.1.0"
description = "MCP server for video content extraction: audio extraction, video trimming, frame extraction, and scene detection"
readme = "README.md"
requires-python = ">=3.12"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["mcp", "video", "audio", "extraction", "ffmpeg", "media-processing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Video",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "ffmpeg-python>=0.2.0",
    "mcp[cli]>=1.9.0",
]

[project.scripts]
video-content-extractor-mcp = "video_content_extractor_mcp.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/video_content_extractor_mcp"]

[dependency-groups]
dev = [
    "build>=1.3.0",
    "twine>=6.1.0",
]
