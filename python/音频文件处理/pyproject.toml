[project]
name = "audio-processor-mcp"
version = "0.1.0"
description = "MCP server for audio processing: format conversion, metadata editing, volume adjustment, and audio analysis"
readme = "README.md"
requires-python = ">=3.12"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["mcp", "audio", "processing", "conversion", "ffmpeg", "metadata", "volume"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Multimedia :: Sound/Audio :: Conversion",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "ffmpeg-python>=0.2.0",
    "mcp[cli]>=1.9.0",
]

[project.scripts]
audio-processor-mcp = "audio_file_processor_mcp.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/audio_file_processor_mcp"]

[dependency-groups]
dev = [
    "build>=1.3.0",
    "twine>=6.1.0",
]
