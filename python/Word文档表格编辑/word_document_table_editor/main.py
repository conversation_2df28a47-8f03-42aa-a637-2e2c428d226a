"""
Word文档表格编辑MCP服务主程序

提供Word文档表格编辑功能的MCP服务器
"""

import os
import sys
# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    format_table,
    set_table_cell_shading,
    apply_table_alternating_rows,
    highlight_table_header,
    merge_table_cells,
    merge_table_cells_horizontal,
    merge_table_cells_vertical,
    set_table_cell_alignment,
    set_table_alignment_all,
    format_table_cell_text,
    set_table_cell_padding,
    set_table_column_width,
    set_table_column_widths,
    set_table_width,
    auto_fit_table_columns
)

# 初始化FastMCP服务器
mcp = FastMCP("Word文档表格编辑")


def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def format_table_tool(filename: str, table_index: int, has_header_row: bool = None,
                         border_style: str = None, shading: list = None):
        """格式化表格的边框、阴影和结构"""
        import asyncio
        return asyncio.run(format_table(filename, table_index, has_header_row, border_style, shading))

    @mcp.tool()
    def set_table_cell_shading_tool(filename: str, table_index: int, row_index: int, 
                                   col_index: int, fill_color: str, pattern: str = "clear"):
        """为特定表格单元格应用阴影/填充"""
        import asyncio
        return asyncio.run(set_table_cell_shading(filename, table_index, row_index, col_index, fill_color, pattern))

    @mcp.tool()
    def apply_table_alternating_rows_tool(filename: str, table_index: int, 
                                        color1: str = "FFFFFF", color2: str = "F2F2F2"):
        """为表格应用交替行颜色以提高可读性"""
        import asyncio
        return asyncio.run(apply_table_alternating_rows(filename, table_index, color1, color2))

    @mcp.tool()
    def highlight_table_header_tool(filename: str, table_index: int, 
                                   header_color: str = "4472C4", text_color: str = "FFFFFF"):
        """为表格标题行应用特殊高亮"""
        import asyncio
        return asyncio.run(highlight_table_header(filename, table_index, header_color, text_color))

    @mcp.tool()
    def merge_table_cells_tool(filename: str, table_index: int, start_row: int, start_col: int, 
                              end_row: int, end_col: int):
        """合并表格中矩形区域的单元格"""
        import asyncio
        return asyncio.run(merge_table_cells(filename, table_index, start_row, start_col, end_row, end_col))

    @mcp.tool()
    def merge_table_cells_horizontal_tool(filename: str, table_index: int, row_index: int, 
                                         start_col: int, end_col: int):
        """水平合并单行中的单元格"""
        import asyncio
        return asyncio.run(merge_table_cells_horizontal(filename, table_index, row_index, start_col, end_col))

    @mcp.tool()
    def merge_table_cells_vertical_tool(filename: str, table_index: int, col_index: int, 
                                       start_row: int, end_row: int):
        """垂直合并单列中的单元格"""
        import asyncio
        return asyncio.run(merge_table_cells_vertical(filename, table_index, col_index, start_row, end_row))

    @mcp.tool()
    def set_table_cell_alignment_tool(filename: str, table_index: int, row_index: int, col_index: int,
                                     horizontal: str = "left", vertical: str = "top"):
        """设置特定表格单元格的文本对齐方式"""
        import asyncio
        return asyncio.run(set_table_cell_alignment(filename, table_index, row_index, col_index, horizontal, vertical))

    @mcp.tool()
    def set_table_alignment_all_tool(filename: str, table_index: int, 
                                    horizontal: str = "left", vertical: str = "top"):
        """设置表格中所有单元格的文本对齐方式"""
        import asyncio
        return asyncio.run(set_table_alignment_all(filename, table_index, horizontal, vertical))

    @mcp.tool()
    def format_table_cell_text_tool(filename: str, table_index: int, row_index: int, col_index: int,
                                   text_content: str = None, bold: bool = None, italic: bool = None,
                                   underline: bool = None, color: str = None, font_size: int = None,
                                   font_name: str = None):
        """格式化特定表格单元格内的文本"""
        import asyncio
        return asyncio.run(format_table_cell_text(filename, table_index, row_index, col_index,
                                                text_content, bold, italic, underline, color, font_size, font_name))

    @mcp.tool()
    def set_table_cell_padding_tool(filename: str, table_index: int, row_index: int, col_index: int,
                                   top: float = None, bottom: float = None, left: float = None, 
                                   right: float = None, unit: str = "points"):
        """设置特定表格单元格的内边距/边距"""
        import asyncio
        return asyncio.run(set_table_cell_padding(filename, table_index, row_index, col_index,
                                                top, bottom, left, right, unit))

    @mcp.tool()
    def set_table_column_width_tool(filename: str, table_index: int, col_index: int, 
                                   width: float, width_type: str = "points"):
        """设置特定表格列的宽度"""
        import asyncio
        return asyncio.run(set_table_column_width(filename, table_index, col_index, width, width_type))

    @mcp.tool()
    def set_table_column_widths_tool(filename: str, table_index: int, widths: list, 
                                    width_type: str = "points"):
        """设置多个表格列的宽度"""
        import asyncio
        return asyncio.run(set_table_column_widths(filename, table_index, widths, width_type))

    @mcp.tool()
    def set_table_width_tool(filename: str, table_index: int, width: float, 
                            width_type: str = "points"):
        """设置表格的整体宽度"""
        import asyncio
        return asyncio.run(set_table_width(filename, table_index, width, width_type))

    @mcp.tool()
    def auto_fit_table_columns_tool(filename: str, table_index: int):
        """设置表格列根据内容自动调整宽度"""
        import asyncio
        return asyncio.run(auto_fit_table_columns(filename, table_index))


def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word文档表格编辑MCP服务器...")
    print("提供以下功能:")
    print("- format_table_tool: 格式化表格")
    print("- set_table_cell_shading_tool: 设置单元格阴影")
    print("- apply_table_alternating_rows_tool: 应用交替行颜色")
    print("- highlight_table_header_tool: 高亮表格标题")
    print("- merge_table_cells_tool: 合并单元格")
    print("- merge_table_cells_horizontal_tool: 水平合并单元格")
    print("- merge_table_cells_vertical_tool: 垂直合并单元格")
    print("- set_table_cell_alignment_tool: 设置单元格对齐")
    print("- set_table_alignment_all_tool: 设置表格整体对齐")
    print("- format_table_cell_text_tool: 格式化单元格文本")
    print("- set_table_cell_padding_tool: 设置单元格内边距")
    print("- set_table_column_width_tool: 设置列宽度")
    print("- set_table_column_widths_tool: 设置多列宽度")
    print("- set_table_width_tool: 设置表格宽度")
    print("- auto_fit_table_columns_tool: 自动调整列宽")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word文档表格编辑服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
