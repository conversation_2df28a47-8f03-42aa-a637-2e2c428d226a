[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-document-table-editor-mcp"
version = "1.0.0"
description = "Word文档表格编辑MCP服务 - 提供文档表格编辑相关功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
]

[project.scripts]
word-document-table-editor = "word_document_table_editor.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_document_table_editor*"]
