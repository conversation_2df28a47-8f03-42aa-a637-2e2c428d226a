#!/usr/bin/env python3
"""
测试脚本 - 验证转换器功能
"""

import asyncio
import tempfile
import os
from pathlib import Path

# 测试导入
try:
    from src.multi_doc_mcp.converters import (
        ExcelToMarkdownConverter,
        PDFToMarkdownConverter,
        PPTToMarkdownConverter,
        WordToMarkdownConverter,
        MarkdownToWordConverter
    )
    from src.multi_doc_mcp.utils import FileUtils, ValidationUtils
    print("✅ 所有模块导入成功!")
except Exception as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

def test_validation_utils():
    """测试验证工具"""
    print("\n🔍 测试验证工具...")
    
    # 测试文件格式验证
    result = ValidationUtils.validate_file_format("test.xlsx", "excel")
    assert not result['valid'], "应该验证失败（文件不存在）"
    print("  ✅ 文件格式验证正常")
    
    # 测试Markdown内容验证
    content = "# 标题\n\n这是测试内容"
    result = ValidationUtils.validate_markdown_content(content)
    assert result['valid'], "应该验证成功"
    print("  ✅ Markdown内容验证正常")

def test_file_utils():
    """测试文件工具"""
    print("\n📁 测试文件工具...")
    
    # 测试临时文件创建
    content = "# 测试文件\n\n这是测试内容"
    temp_path = FileUtils.create_temp_file(content, '.md')
    assert os.path.exists(temp_path), "临时文件应该存在"
    print(f"  ✅ 临时文件创建成功: {temp_path}")
    
    # 测试文件信息获取
    info = FileUtils.get_file_info(temp_path)
    assert 'size' in info, "应该包含文件大小信息"
    print(f"  ✅ 文件信息获取成功: {info['size']} bytes")
    
    # 清理
    FileUtils.cleanup_temp_file(temp_path)

def test_markdown_converter():
    """测试Markdown转换器"""
    print("\n📝 测试Markdown转Word转换器...")
    
    converter = MarkdownToWordConverter()
    
    # 创建示例文件
    result = converter.create_sample_markdown("test_sample.md", "user_guide")
    assert result['success'], "示例文件创建应该成功"
    print(f"  ✅ 示例文件创建成功: {result['filename']}")
    
    # 验证文件
    validation = converter.validate_markdown("test_sample.md")
    assert validation['is_valid'], "示例文件应该有效"
    print("  ✅ Markdown文件验证成功")
    
    # 清理
    os.unlink("test_sample.md")

async def main():
    """主测试函数"""
    print("🚀 开始测试Markdown格式转换MCP服务...")
    
    try:
        test_validation_utils()
        test_file_utils()
        test_markdown_converter()
        
        print("\n🎉 所有测试通过！")
        print("\n📋 MCP服务包含的转换器:")
        print("  - Excel → Markdown")
        print("  - PDF → Markdown") 
        print("  - PPT → Markdown")
        print("  - Word → Markdown")
        print("  - Markdown → Word")
        print("\n✨ MCP服务已准备就绪，可以在Claude Desktop中配置使用！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())