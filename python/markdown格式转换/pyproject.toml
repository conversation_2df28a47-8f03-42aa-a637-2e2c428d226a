[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "markdown-format-converter-mcp"
version = "1.0.0"
description = "统一的文档格式转换MCP服务器 - 支持Excel、PDF、PPT、Word与Markdown互转"
authors = [
    {name = "MCP Team"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "mcp>=1.0.0",
    "markitdown[all]>=0.0.1",
    "pypandoc-binary>=1.11",
    "python-docx>=1.1.0",
    "openpyxl>=3.1.0"
]

[project.scripts]
markdown-format-converter-mcp = "multi_doc_mcp.__main__:run"

[tool.hatch.build.targets.wheel]
packages = ["src/multi_doc_mcp"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
]

