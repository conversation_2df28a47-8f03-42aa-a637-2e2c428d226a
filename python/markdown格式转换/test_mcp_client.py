#!/usr/bin/env python3
"""
MCP客户端测试脚本 - 测试工具列表
"""

import asyncio
import json
import subprocess
from mcp.client.session import ClientSession  
from mcp.client.stdio import StdioServerParameters, stdio_client


async def test_mcp_server():
    """测试MCP服务器"""
    print("🔧 启动MCP客户端测试...")
    
    # 创建服务器参数
    server_params = StdioServerParameters(
        command="uv",
        args=[
            "run", 
            "markdown-format-converter-mcp"
        ],
        cwd="/Users/<USER>/Desktop/mcp/skills/python/markdown格式转换"
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化会话
                await session.initialize()
                print("✅ MCP会话初始化成功")
                
                # 列出所有工具
                tools_result = await session.list_tools()
                print(f"\n📋 发现 {len(tools_result.tools)} 个转换工具:")
                
                for i, tool in enumerate(tools_result.tools, 1):
                    print(f"  {i}. {tool.name}")
                    print(f"     📝 {tool.description}")
                
                print(f"\n🎉 MCP服务器运行正常！包含以下转换功能:")
                expected_tools = [
                    "excel_to_markdown", 
                    "pdf_to_markdown", 
                    "ppt_to_markdown", 
                    "word_to_markdown", 
                    "markdown_to_word"
                ]
                
                found_tools = [tool.name for tool in tools_result.tools]
                for tool_name in expected_tools:
                    if tool_name in found_tools:
                        print(f"  ✅ {tool_name}")
                    else:
                        print(f"  ❌ {tool_name} (缺失)")
                
                return True
                
    except Exception as e:
        print(f"❌ MCP测试失败: {e}")
        return False


async def main():
    """主函数"""
    success = await test_mcp_server()
    
    if success:
        print(f"\n🚀 MCP服务测试完成！")
        print(f"\n📖 Claude Desktop配置:")
        config = {
            "markdown-format-converter": {
                "name": "Markdown格式转换",
                "type": "stdio", 
                "description": "统一的文档格式转换工具",
                "isActive": True,
                "command": "uv",
                "args": [
                    "--directory",
                    "/Users/<USER>/Desktop/mcp/skills/python/markdown格式转换",
                    "run",
                    "markdown-format-converter-mcp"
                ]
            }
        }
        print(json.dumps(config, indent=2, ensure_ascii=False))
    else:
        print("\n❌ MCP服务测试失败")


if __name__ == "__main__":
    asyncio.run(main())