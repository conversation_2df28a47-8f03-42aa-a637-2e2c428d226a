[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-document-content-adder-mcp"
version = "1.0.0"
description = "Word文档内容添加MCP服务 - 提供文档内容添加相关功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "Pillow>=10.0.0",
]

[project.scripts]
word-document-content-adder = "word_document_content_adder.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_document_content_adder*"]
