#!/bin/bash

# PyPI上传脚本 - 支持测试仓库和正式仓库
# 使用方法: ./upload_to_pypi.sh <项目目录名> [test|prod]

set -e  # 遇到错误立即退出

# 配置
TEST_TOKEN="pypi-AgENdGVzdC5weXBpLm9yZwIkNzc0N2JkMDItNDVhNi00ZGMzLWIzYTAtMjEyYjQzODhjZTk1AAIqWzMsImNiYTE1MmMzLTIwNjAtNGZhYy1iZWE4LTVmOWNlMDVmNmY5NCJdAAAGII4O3m5uDABRGF6fYAftimqLz__6JF8Q_xBvMtn6sdvY"
PROD_TOKEN="***********************************************************************************************************************************************************************************"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${BLUE}🔄 $1${NC}"
}

# 使用说明
show_usage() {
    echo "使用方法: $0 <项目目录名> [test|prod] [--auto-version]"
    echo ""
    echo "参数："
    echo "  项目目录名      要上传的Python项目目录"
    echo "  test|prod       目标仓库 (默认: test)"
    echo "  --auto-version  自动递增版本号 (可选)"
    echo ""
    echo "示例："
    echo "  $0 my_package test              # 上传到测试仓库"
    echo "  $0 my_package prod              # 上传到正式仓库"
    echo "  $0 my_package test --auto-version  # 自动递增版本号并上传到测试仓库"
    echo "  $0 my_package --auto-version    # 自动递增版本号并上传到测试仓库"
}

# 自动递增版本号
auto_increment_version() {
    local config_file="$1"
    local current_version
    
    if [ -f "$config_file" ]; then
        # 从pyproject.toml提取版本号
        current_version=$(grep -E '^version\s*=' "$config_file" | sed 's/.*=\s*"\([^"]*\)".*/\1/')
        
        if [ -z "$current_version" ]; then
            log_error "无法从 $config_file 中提取版本号"
            exit 1
        fi
        
        log_info "当前版本: $current_version"
        
        # 解析版本号 (假设格式为 major.minor.patch)
        local major minor patch
        IFS='.' read -r major minor patch <<< "$current_version"
        
        # 递增patch版本
        patch=$((patch + 1))
        local new_version="${major}.${minor}.${patch}"
        
        log_info "新版本: $new_version"
        
        # 更新pyproject.toml中的版本号
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/version = \"$current_version\"/version = \"$new_version\"/" "$config_file"
        else
            # Linux
            sed -i "s/version = \"$current_version\"/version = \"$new_version\"/" "$config_file"
        fi
        
        log_success "版本号已更新为: $new_version"
    else
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "缺少项目目录参数"
    show_usage
    exit 1
fi

PROJECT_DIR="$1"
REPO_TYPE="prod"  # 默认为test
AUTO_VERSION=false

# 解析参数
shift  # 移除第一个参数(项目目录)
while [[ $# -gt 0 ]]; do
    case $1 in
        test|prod)
            REPO_TYPE="$1"
            ;;
        --auto-version)
            AUTO_VERSION=true
            ;;
        *)
            log_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
    shift
done

# 设置token和仓库URL
if [ "$REPO_TYPE" = "test" ]; then
    TOKEN="$TEST_TOKEN"
    REPO_URL="https://test.pypi.org/legacy/"
    REPO_NAME="测试"
else
    TOKEN="$PROD_TOKEN"
    REPO_URL="https://upload.pypi.org/legacy/"
    REPO_NAME="正式"
fi

# 检查项目目录
if [ ! -d "$PROJECT_DIR" ]; then
    log_error "项目目录不存在: $PROJECT_DIR"
    exit 1
fi

# 检查项目配置文件
if [ ! -f "$PROJECT_DIR/pyproject.toml" ] && [ ! -f "$PROJECT_DIR/setup.py" ]; then
    log_error "项目缺少配置文件: pyproject.toml 或 setup.py"
    exit 1
fi

log_info "开始上传项目: $PROJECT_DIR"
log_info "目标仓库: ${REPO_NAME}仓库"
echo "=================================================="

# 进入项目目录
cd "$PROJECT_DIR"

# 检测包管理器和Python环境
if [ -f "uv.lock" ] && command -v uv &> /dev/null; then
    PACKAGE_MANAGER="uv"
    PYTHON_CMD="uv run python"
    log_info "检测到uv项目，使用: $PYTHON_CMD"
elif command -v uv &> /dev/null; then
    PACKAGE_MANAGER="uv"
    PYTHON_CMD="uv run python"
    log_info "使用uv环境: $PYTHON_CMD"
elif command -v python3 &> /dev/null; then
    PACKAGE_MANAGER="pip"
    PYTHON_CMD="python3"
    log_info "使用系统Python: $PYTHON_CMD"
elif command -v python &> /dev/null; then
    PACKAGE_MANAGER="pip"
    PYTHON_CMD="python"
    log_info "使用系统Python: $PYTHON_CMD"
else
    log_error "未找到Python解释器或包管理器"
    exit 1
fi

# 检查并安装依赖
log_step "检查依赖..."

# 检查twine
if [ "$PACKAGE_MANAGER" = "uv" ]; then
    if ! uv run twine --version &> /dev/null; then
        log_step "安装twine..."
        uv add --dev twine
    fi
else
    if ! command -v twine &> /dev/null; then
        log_step "安装twine..."
        pip install twine
    fi
fi

# 检查build
if [ "$PACKAGE_MANAGER" = "uv" ]; then
    if ! uv run python -m build --version &> /dev/null; then
        log_step "安装build..."
        uv add --dev build
    fi
else
    if ! $PYTHON_CMD -m build --version &> /dev/null; then
        log_step "安装build..."
        pip install build
    fi
fi

# 自动递增版本号（如果启用）
if [ "$AUTO_VERSION" = true ]; then
    log_step "自动递增版本号..."
    auto_increment_version "pyproject.toml"
fi

log_success "依赖检查完成"

# 清理旧的构建文件
if [ -d "dist" ]; then
    log_step "清理旧的构建文件..."
    rm -rf dist/
fi

if [ -d "build" ]; then
    rm -rf build/
fi

if [ -d "*.egg-info" ]; then
    rm -rf *.egg-info/
fi

log_success "清理完成"

# 构建包
log_step "构建包..."
$PYTHON_CMD -m build

# 检查构建结果
if [ ! -d "dist" ] || [ -z "$(ls -A dist/)" ]; then
    log_error "构建失败，没有生成dist文件"
    exit 1
fi

log_success "构建完成"
log_info "生成的文件:"
ls -la dist/

# 创建临时的.pypirc文件
TEMP_PYPIRC=$(mktemp)
cat > "$TEMP_PYPIRC" << EOF
[distutils]
index-servers = upload

[upload]
repository = $REPO_URL
username = __token__
password = $TOKEN
EOF

# 上传到PyPI
log_step "上传包到${REPO_NAME}仓库..."

# 使用临时配置文件上传
if [ "$PACKAGE_MANAGER" = "uv" ]; then
    UPLOAD_CMD="uv run twine upload --config-file \"$TEMP_PYPIRC\" --repository upload --verbose dist/*"
else
    UPLOAD_CMD="twine upload --config-file \"$TEMP_PYPIRC\" --repository upload --verbose dist/*"
fi

# 捕获上传输出
UPLOAD_OUTPUT=$(eval $UPLOAD_CMD 2>&1)
UPLOAD_EXIT_CODE=$?

if [ $UPLOAD_EXIT_CODE -eq 0 ]; then
    log_success "上传成功到${REPO_NAME}仓库!"
    
    # 提供安装提示
    echo ""
    echo "=================================================="
    if [ "$REPO_TYPE" = "test" ]; then
        log_info "测试安装命令:"
        echo "pip install --index-url https://test.pypi.org/simple/ --extra-index-url https://pypi.org/simple/ <包名>"
    else
        log_info "安装命令:"
        echo "pip install <包名>"
    fi
    echo "=================================================="
else
    log_error "上传失败!"
    echo ""
    log_error "详细错误信息:"
    echo "$UPLOAD_OUTPUT"
    echo ""
    
    # 分析常见错误并提供解决方案
    if echo "$UPLOAD_OUTPUT" | grep -i "400 Bad Request" > /dev/null; then
        log_warning "可能的原因："
        echo "  1. 版本号已存在 - 请在 pyproject.toml 中更新版本号"
        echo "  2. 包名冲突 - 请检查包名是否唯一"
        echo "  3. 元数据格式错误 - 请检查 pyproject.toml 配置"
    elif echo "$UPLOAD_OUTPUT" | grep -i "403 Forbidden" > /dev/null; then
        log_warning "可能的原因："
        echo "  1. Token 权限不足或已过期"
        echo "  2. 包名已被其他用户占用"
    elif echo "$UPLOAD_OUTPUT" | grep -i "401 Unauthorized" > /dev/null; then
        log_warning "可能的原因："
        echo "  1. Token 无效或格式错误"
        echo "  2. Token 权限不足"
    fi
    
    rm -f "$TEMP_PYPIRC"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_PYPIRC"

log_success "脚本执行完成!"