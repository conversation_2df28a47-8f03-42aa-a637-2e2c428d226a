# Smithery configuration file: https://smithery.ai/docs/build/project-config

startCommand:
  type: stdio
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => ({ command: 'bilibili-video-info-mcp', args: ['stdio'], env: { SESSDATA: config.sessdata } })
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - sessdata
    properties:
      sessdata:
        type: string
        description: Bilibili SESSDATA cookie for authentication.
  exampleConfig:
    sessdata: your_valid_sessdata_here
