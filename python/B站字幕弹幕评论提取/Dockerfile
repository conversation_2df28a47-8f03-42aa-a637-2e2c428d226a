# Generated by https://smithery.ai. See: https://smithery.ai/docs/build/project-config
FROM python:3.10-slim

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy project
COPY . /app

# Install Python dependencies
RUN pip install --no-cache-dir .

# Default environment variables
ENV PYTHONUNBUFFERED=1

# Expose port for HTTP transports if needed
EXPOSE 8000

# Default entrypoint
ENTRYPOINT ["bilibili-video-info-mcp"]
CMD ["stdio"]
