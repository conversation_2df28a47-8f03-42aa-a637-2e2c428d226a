# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.10-alpine

# Install system dependencies (if any required, e.g., for pillow)
RUN apk add --no-cache gcc musl-dev libffi-dev

# Set work directory
WORKDIR /app

# Copy the application code
COPY . .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Expose port if needed (not needed for stdio)

# Set the entrypoint to run the MCP server
ENTRYPOINT ["python", "ppt_mcp_server.py"]
