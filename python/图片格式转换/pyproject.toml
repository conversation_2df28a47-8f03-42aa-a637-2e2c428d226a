[project]
name = "image-convertor-mcp"
version = "0.1.7"
description = "Model Context Protocol Server for Image File Format Conversion"
readme = "README.md"
requires-python = ">=3.9"
authors = [
  {name = "<PERSON>"}
]
maintainers = [
  {name = "Beta"}
]
license = {file = "LICENSE"}
keywords = ["mcp", "image-conversion", "image-processing", "gif", "pdf", "model-context-protocol"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Image Processing",
]
dependencies = [
    "mcp>=0.1.0",
    "Pillow>=10.0.0",
    "pillow-heif>=0.15.0",
    "reportlab>=4.0.0",
]

[project.urls]
Homepage = "https://github.com/Beta0415/image-convertor-mcp"
Repository = "https://github.com/Beta0415/image-convertor-mcp"
Documentation = "https://github.com/Beta0415/image-convertor-mcp#readme"
Issues = "https://github.com/Beta0415/image-convertor-mcp/issues"

[project.scripts]
image-convertor-mcp = "image_convertor_mcp.__main__:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["image_convertor_mcp"]

[tool.hatch.build.targets.sdist]
include = [
  "image_convertor_mcp/**/*.py",
  "/README.md",
  "/LICENSE",
  "/pyproject.toml"
]
