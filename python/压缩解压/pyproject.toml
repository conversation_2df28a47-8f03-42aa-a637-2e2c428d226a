[project]
name = "archive-mcp"
version = "0.2.0"
description = "A comprehensive MCP server for file compression and extraction operations"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp>=1.0.0",
    "py7zr>=0.20.0",
    "pydantic>=2.0.0",
    "pyminizip>=0.2.6",
    "pyzipper>=0.3.6",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
archive-mcp = "archive_mcp.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src/archive_mcp"]

[tool.uv]
dev-dependencies = [
    "build>=1.3.0",
    "pytest>=8.0.0",
    "twine>=6.1.0",
]
