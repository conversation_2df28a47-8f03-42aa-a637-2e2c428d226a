[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-document-text-editor-mcp"
version = "1.0.0"
description = "Word文档文本编辑MCP服务 - 提供文档文本编辑相关功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
]

[project.scripts]
word-document-text-editor = "word_document_text_editor.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_document_text_editor*"]
